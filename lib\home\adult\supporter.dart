import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pro/core/di/di.dart';
import 'package:pro/cubit/sos/sos_cubit.dart';
import 'package:pro/cubit/sos/sos_state.dart';
import 'package:pro/home/<USER>/location_map.dart';
import 'package:pro/home/<USER>/profile_page.dart';
import 'package:pro/home/<USER>/settings_page_supporter.dart';
import 'package:pro/home/<USER>/supporter_menu_list.dart';
import 'package:pro/models/SosNotificationModel.dart';
import 'package:pro/services/notification_service.dart';
import 'package:pro/widgets/custom_bottom_bar.dart';
import 'package:pro/widgets/header_profile.dart';
import 'package:pro/widgets/notification_icon.dart';

class Supporter extends StatefulWidget {
  const Supporter({super.key});

  @override
  State<Supporter> createState() => _TravelerState();
}

class _TravelerState extends State<Supporter> {
  int currentIndex = 0; // لمتابعة العنصر النشط في BottomNavigationBar

  // Pages that can be changed later to real pages
  final List<Widget> pages = [
    const HomePage(), // Home page with new design
    SettingsScreen2(),
    MapScreen(),
    SupportersListWidget(),
    ProfilePage(),
  ];

  @override
  void initState() {
    super.initState();
    // Initialize notification service
    getIt<NotificationService>().initialize();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) {
        final sosCubit = getIt<SosCubit>();
        sosCubit.initialize();
        // Set context for showing dialogs
        sosCubit.setContext(context);
        return sosCubit;
      },
      child: BlocListener<SosCubit, SosState>(
        listener: (context, state) {
          if (state is SosNotificationReceived) {
            // Show a snackbar when a notification is received
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content:
                    Text('SOS Alert from ${state.notification.travelerName}!'),
                backgroundColor: Colors.red,
                duration: const Duration(seconds: 5),
                action: SnackBarAction(
                  label: 'View',
                  textColor: Colors.white,
                  onPressed: () {
                    getIt<NotificationService>()
                        .showSosDialog(context, state.notification);
                  },
                ),
              ),
            );
          }
        },
        child: Scaffold(
          resizeToAvoidBottomInset: false, // Prevent overlap with keyboard
          body: pages[currentIndex], // Display page based on currentIndex
          bottomNavigationBar: CustomBottomBar(
            currentIndex: currentIndex,
            onTap: (index) {
              setState(() {
                currentIndex = index;
              });
            },
            icons: bottomBarIcons,
          ),
        ),
      ),
    );
  }
}

final List<IconData> bottomBarIcons = [
  Icons.home,
  Icons.settings,
  Icons.location_on_outlined,
  Icons.menu,
  Icons.person,
];

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  void onMapTap() {
    // Action when map is tapped
    debugPrint('Map tapped!');
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.only(bottom: 10),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            const SizedBox(height: 40),
            const HeaderProfile(showNotifications: true, userType: 'supporter'),
            const SizedBox(height: 40),
            MapCard(
              name: 'Traveler_Ayman',
              status: 'Safe',
              date: 'Friday, 23 October',
              time: '11:00-13:00',
              address: 'South Street Journal SR, Nike Jc 23/34',
              isActive: true,
              onMapTap: onMapTap,
            ),
            const SizedBox(height: 20),
            MapCard(
              name: 'Traveler_Ayman',
              status: 'Safe',
              date: 'Friday, 23 October',
              time: '11:00-13:00',
              address: 'South Street Journal SR, Nike Jc 23/34',
              isActive: false,
              onMapTap: onMapTap,
            ),
          ],
        ),
      ),
    );
  }
}

class MapCard extends StatelessWidget {
  final String name;
  final String status;
  final String date;
  final String time;
  final String address;
  final bool isActive;
  final VoidCallback onMapTap;

  const MapCard({
    Key? key,
    required this.name,
    required this.status,
    required this.date,
    required this.time,
    required this.address,
    required this.isActive,
    required this.onMapTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 10),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isActive
            ? Colors.green
            : Colors.grey[400], // اللون حسب النشاط في الكونتينر الكبير
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ListTile(
            contentPadding: EdgeInsets.zero,
            leading: const CircleAvatar(
              radius: 25,
              backgroundImage:
                  AssetImage('assets/images/man.jpeg'), // صورة شخصية
            ),
            title: Text(
              name,
              style: TextStyle(
                  color: isActive ? Colors.white : Colors.black,
                  fontFamily: 'Poppins',
                  fontSize: 13),
            ),
            subtitle: Row(
              children: [
                // الكونتينر الذي يحتوي على القلب وكلمة "Safe"
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.white, // اللون ثابت أبيض للـ Container
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.favorite,
                        color: isActive
                            ? Colors.green
                            : Colors.grey, // تغير اللون عند النشاط
                        size: 16,
                      ),
                      const SizedBox(width: 5),
                      Text(
                        status,
                        style: TextStyle(
                            color: isActive ? Colors.green : Colors.grey,
                            fontFamily: 'Poppins' // تغير اللون عند النشاط
                            ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          InkWell(
            onTap: () {
              Navigator.push(context, MaterialPageRoute(builder: (context) {
                return MapScreen();
              }));
            },
            child: Container(
                height: 150,
                width: double.infinity,
                margin: const EdgeInsets.symmetric(vertical: 10),
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Image.asset(
                  'assets/images/map.jpg',
                  fit: BoxFit.cover,
                )),
          ),
          Row(
            children: [
              Icon(Icons.calendar_today,
                  color: isActive ? Colors.white : Colors.black54),
              const SizedBox(width: 10),
              Text(
                date,
                style: TextStyle(
                    color: isActive ? Colors.white : Colors.black54,
                    fontFamily: 'Poppins'),
              ),
            ],
          ),
          const SizedBox(height: 15),
          // Container(
          //   padding: EdgeInsets.only(left: 100),
          //   width: 2, // عرض الخط
          //   height: 20, // طول الخط
          //   color: Colors.white, // لون الخط
          //   // margin: EdgeInsets.only(right: 0), // مسافة بين الخط والمحتوى الآخر
          // ),
          Row(
            children: [
              Icon(Icons.access_time,
                  color: isActive ? Colors.white : Colors.black54),
              const SizedBox(width: 10),
              Text(
                time,
                style: TextStyle(
                    color: isActive ? Colors.white : Colors.black54,
                    fontFamily: 'Poppins'),
              ),
            ],
          ),
          const SizedBox(height: 15),
          Row(
            children: [
              Icon(Icons.location_on,
                  color: isActive ? Colors.white : Colors.black54),
              const SizedBox(width: 10),
              Expanded(
                child: Text(
                  address,
                  style: TextStyle(
                      color: isActive ? Colors.white : Colors.black54,
                      fontFamily: 'Poppins'),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
