import 'dart:developer';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:pro/models/OfflineContactModel.dart';
import 'package:pro/cache/CacheHelper.dart';
import 'package:pro/core/API/ApiKey.dart';

class OfflineContactService {
  static const String _boxName = 'offline_contacts';
  static const String _lastSyncKey = 'last_sync_timestamp';
  static Box<OfflineContactModel>? _box;

  // Initialize Hive and open the box
  static Future<void> init() async {
    try {
      await Hive.initFlutter();
      
      // Register the adapter if not already registered
      if (!Hive.isAdapterRegistered(0)) {
        Hive.registerAdapter(OfflineContactModelAdapter());
      }
      
      _box = await Hive.openBox<OfflineContactModel>(_boxName);
      log('OfflineContactService initialized successfully');
    } catch (e) {
      log('Error initializing OfflineContactService: $e');
      rethrow;
    }
  }

  // Get the current user ID
  static String? _getCurrentUserId() {
    return CacheHelper.getData(key: ApiKey.userId) ??
        CacheHelper.getData(key: "current_user_id") ??
        CacheHelper.getData(key: ApiKey.id) ??
        CacheHelper.getData(key: "userId") ??
        CacheHelper.getData(key: "UserId") ??
        CacheHelper.getData(key: "sub");
  }

  // Save contacts to local storage
  static Future<void> saveContacts(List<OfflineContactModel> contacts) async {
    try {
      if (_box == null) await init();
      
      final currentUserId = _getCurrentUserId();
      if (currentUserId == null) {
        throw Exception('User ID not found');
      }

      // Clear existing contacts for current user
      await clearContactsForCurrentUser();

      // Save new contacts
      for (final contact in contacts) {
        final key = '${currentUserId}_${contact.id}';
        await _box!.put(key, contact);
      }

      // Update last sync timestamp
      await _updateLastSyncTime();
      
      log('Saved ${contacts.length} contacts for user $currentUserId');
    } catch (e) {
      log('Error saving contacts: $e');
      rethrow;
    }
  }

  // Get all contacts for current user
  static Future<List<OfflineContactModel>> getContacts() async {
    try {
      if (_box == null) await init();
      
      final currentUserId = _getCurrentUserId();
      if (currentUserId == null) {
        log('User ID not found, returning empty contacts list');
        return [];
      }

      final contacts = <OfflineContactModel>[];
      
      for (final key in _box!.keys) {
        if (key.toString().startsWith('${currentUserId}_')) {
          final contact = _box!.get(key);
          if (contact != null) {
            contacts.add(contact);
          }
        }
      }

      log('Retrieved ${contacts.length} contacts for user $currentUserId');
      return contacts;
    } catch (e) {
      log('Error getting contacts: $e');
      return [];
    }
  }

  // Get phone numbers only for SMS sending
  static Future<List<String>> getPhoneNumbers() async {
    try {
      final contacts = await getContacts();
      final phoneNumbers = contacts
          .map((contact) => contact.phoneNumber)
          .where((phone) => phone.isNotEmpty)
          .toList();
      
      log('Retrieved ${phoneNumbers.length} phone numbers for SMS');
      return phoneNumbers;
    } catch (e) {
      log('Error getting phone numbers: $e');
      return [];
    }
  }

  // Clear contacts for current user
  static Future<void> clearContactsForCurrentUser() async {
    try {
      if (_box == null) await init();
      
      final currentUserId = _getCurrentUserId();
      if (currentUserId == null) return;

      final keysToDelete = <String>[];
      for (final key in _box!.keys) {
        if (key.toString().startsWith('${currentUserId}_')) {
          keysToDelete.add(key.toString());
        }
      }

      for (final key in keysToDelete) {
        await _box!.delete(key);
      }

      log('Cleared contacts for user $currentUserId');
    } catch (e) {
      log('Error clearing contacts: $e');
    }
  }

  // Check if sync is needed (48 hours)
  static Future<bool> isSyncNeeded() async {
    try {
      final lastSync = CacheHelper.getData(key: _lastSyncKey);
      if (lastSync == null) return true;

      final lastSyncTime = DateTime.parse(lastSync.toString());
      final now = DateTime.now();
      final difference = now.difference(lastSyncTime);

      final needsSync = difference.inHours >= 48;
      log('Last sync: $lastSyncTime, Hours since: ${difference.inHours}, Needs sync: $needsSync');
      
      return needsSync;
    } catch (e) {
      log('Error checking sync status: $e');
      return true; // Default to needing sync if error
    }
  }

  // Update last sync timestamp
  static Future<void> _updateLastSyncTime() async {
    try {
      await CacheHelper.saveData(
        key: _lastSyncKey,
        value: DateTime.now().toIso8601String(),
      );
      log('Updated last sync timestamp');
    } catch (e) {
      log('Error updating sync timestamp: $e');
    }
  }

  // Get contacts count for current user
  static Future<int> getContactsCount() async {
    try {
      final contacts = await getContacts();
      return contacts.length;
    } catch (e) {
      log('Error getting contacts count: $e');
      return 0;
    }
  }

  // Close the box (call when app is closing)
  static Future<void> close() async {
    try {
      await _box?.close();
      log('OfflineContactService closed');
    } catch (e) {
      log('Error closing OfflineContactService: $e');
    }
  }
}
